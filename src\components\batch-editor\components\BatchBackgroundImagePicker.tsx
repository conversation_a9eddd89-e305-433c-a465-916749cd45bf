'use client';

import { Button } from '@/components/ui/Button';
import { ChevronLeft, ChevronRight, Upload, X } from 'lucide-react';
import Image from 'next/image';
import { useRef, useState, useEffect } from 'react';
import {
  generatePresetCategories,
  type PresetCategory,
} from '@/lib/imageUtils/backgroundUtils';

/**
 * 代表一个已上传的图片。
 */
export interface UploadedImage {
  id: string;
  url: string; // 用于显示的 Object URL
  name: string;
  timestamp: number;
  file?: File; // 原始文件，可选
}

/**
 * 批量背景图片选择器组件的 Props。
 */
interface BatchBackgroundImagePickerProps {
  currentBackgroundImageUrl: string | undefined;
  onSelectImage: (url: string | undefined) => void;
  onFileUpload: (file: File) => void; // For new uploads
  uploadedImages: UploadedImage[];
  // 新增：背景图片管理方法
  onAddBackgroundImage?: (file: File) => Promise<UploadedImage>;
}

// 动态生成的预设分类（使用英文名称）
const PRESET_CATEGORIES: PresetCategory[] = generatePresetCategories(false);

/**
 * 批量编辑专用的背景图片选择器组件
 */
export function BatchBackgroundImagePicker({
  currentBackgroundImageUrl,
  onSelectImage,
  onFileUpload,
  uploadedImages,
  onAddBackgroundImage,
}: BatchBackgroundImagePickerProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [categoryPages, setCategoryPages] = useState<Record<string, number>>(
    {}
  );
  const [uploadedImagesPage, setUploadedImagesPage] = useState(0);

  const getCategoryPage = (categoryId: string) =>
    categoryPages[categoryId] || 0;

  const setCategoryPage = (categoryId: string, page: number) => {
    setCategoryPages(prev => ({
      ...prev,
      [categoryId]: page,
    }));
  };

  // 按时间戳降序排序上传的图片（最新的在前）
  const sortedUploadedImages = [...uploadedImages].sort(
    (a, b) => b.timestamp - a.timestamp
  );

  // 监听uploadedImages变化，重置分页状态避免显示问题
  useEffect(() => {
    const itemsPerPage = 3;
    const totalItems = 2 + sortedUploadedImages.length; // 透明背景 + 上传按钮 + 上传图片
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // 如果当前页超出了有效范围，重置到最后一页
    if (uploadedImagesPage >= totalPages && totalPages > 0) {
      setUploadedImagesPage(Math.max(0, totalPages - 1));
    }
  }, [sortedUploadedImages.length, uploadedImagesPage]);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        // 优先使用新的背景图片管理方法
        if (onAddBackgroundImage) {
          await onAddBackgroundImage(file);
        } else {
          // 回退到原有方法
          onFileUpload(file);
        }
      } catch (error) {
        console.error('上传背景图片失败:', error);
      }
      // 重置文件输入，以便可以再次上传同一个文件
      if (fileInputRef.current) fileInputRef.current.value = '';
    } else {
    }
  };

  // 透明背景的棋盘图案样式
  const checkerboardStyle = {
    backgroundImage: `
      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
    `,
    backgroundSize: '8px 8px',
    backgroundPosition: '0 0, 0 4px, 4px -4px, -4px 0px',
  };

  return (
    <div
      className='space-y-4 overflow-y-auto overflow-x-hidden'
      style={{ height: 'calc(100vh - 300px)' }}
    >
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type='file'
        accept='image/*'
        onChange={handleFileChange}
        className='hidden'
      />

      {/* 上传的图片和透明背景选择区域 */}
      <div className='space-y-2'>
        <div className='flex items-center justify-between'></div>

        {/* 图片滑动区域 - 带左右切换按钮 */}
        <div className='relative px-4'>
          {/* 左侧切换按钮 */}
          {uploadedImagesPage > 0 && (
            <Button
              variant='ghost'
              size='sm'
              className='absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white hover:bg-white shadow-md border border-border cursor-pointer transition-all duration-300 ease-in-out animate-in fade-in slide-in-from-left-2'
              onClick={() =>
                setUploadedImagesPage(Math.max(0, uploadedImagesPage - 1))
              }
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
          )}

          {/* 右侧切换按钮 */}
          {(uploadedImagesPage + 1) * 3 < 2 + sortedUploadedImages.length && (
            <Button
              variant='ghost'
              size='sm'
              className='absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white hover:bg-white shadow-md border border-border cursor-pointer transition-all duration-300 ease-in-out animate-in fade-in slide-in-from-right-2'
              onClick={() => setUploadedImagesPage(uploadedImagesPage + 1)}
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          )}

          {/* 图片容器 */}
          <div className='flex gap-3'>
            <div
              className='flex gap-3 transition-transform duration-300 ease-in-out'
              style={{
                transform: `translateX(-${uploadedImagesPage * (3 * 96 + 2 * 12)}px)`, // 3张图片宽度 + 2个间距
              }}
            >
              {/* 透明背景按钮 */}
              <Button
                variant='outline'
                className={`h-24 w-24 p-0 border-3 rounded-lg flex-shrink-0 ${
                  currentBackgroundImageUrl === undefined
                    ? 'border-[#FFD700] ring-0'
                    : 'border-transparent hover:border-gray-300'
                }`}
                onClick={() => onSelectImage(undefined)}
                style={checkerboardStyle}
              >
                <div
                  className='w-full h-full rounded-lg'
                  style={checkerboardStyle}
                />
              </Button>

              {/* 上传按钮 */}
              <Button
                variant='outline'
                className='h-24 w-24 p-0 border-3 border-dashed border-gray-300 rounded-lg flex-shrink-0 hover:border-gray-400 transition-colors'
                onClick={handleUploadClick}
              >
                <div className='flex flex-col items-center justify-center gap-1'>
                  <Upload className='h-6 w-6 text-gray-500' />
                  <span className='text-xs text-gray-500'>Upload</span>
                </div>
              </Button>

              {/* 上传的图片 */}
              {sortedUploadedImages.map(image => (
                <div key={image.id} className='relative flex-shrink-0'>
                  <Button
                    variant='outline'
                    className={`h-24 w-24 p-0 border-3 rounded-lg overflow-hidden ${
                      currentBackgroundImageUrl === image.url
                        ? 'border-[#FFD700] ring-0'
                        : 'border-transparent hover:border-gray-300'
                    }`}
                    onClick={() => {
                      // 选择图片时设置backgroundImageId
                      onSelectImage(image.url);
                    }}
                  >
                    <Image
                      src={image.url}
                      alt={image.name}
                      width={96}
                      height={96}
                      className='w-full h-full object-cover'
                    />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 预设图片分类 */}
      {PRESET_CATEGORIES.map(category => {
        const currentPage = getCategoryPage(category.id);
        const imagesPerPage = 3;
        const totalPages = Math.ceil(category.images.length / imagesPerPage);
        const startIndex = currentPage * imagesPerPage;
        const visibleImages = category.images.slice(
          startIndex,
          startIndex + imagesPerPage
        );

        return (
          <div key={category.id} className='space-y-2'>
            <div className='flex items-center justify-between'>
              <p className='font-medium text-[#878787] pl-4'>{category.name}</p>
            </div>

            {/* 预设图片网格 - 带左右切换按钮 */}
            <div className='relative px-4'>
              {/* 左侧切换按钮 */}
              {currentPage > 0 && (
                <Button
                  variant='ghost'
                  size='sm'
                  className='absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white hover:bg-white shadow-md border border-border cursor-pointer transition-all duration-300 ease-in-out animate-in fade-in slide-in-from-left-2'
                  onClick={() =>
                    setCategoryPage(category.id, Math.max(0, currentPage - 1))
                  }
                >
                  <ChevronLeft className='h-4 w-4' />
                </Button>
              )}

              {/* 右侧切换按钮 */}
              {currentPage < totalPages - 1 && (
                <Button
                  variant='ghost'
                  size='sm'
                  className='absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white hover:bg-white shadow-md border border-border cursor-pointer transition-all duration-300 ease-in-out animate-in fade-in slide-in-from-right-2'
                  onClick={() =>
                    setCategoryPage(
                      category.id,
                      Math.min(totalPages - 1, currentPage + 1)
                    )
                  }
                >
                  <ChevronRight className='h-4 w-4' />
                </Button>
              )}

              {/* 图片容器 */}
              <div className='flex gap-3'>
                {visibleImages.map(image => (
                  <Button
                    key={image.id}
                    variant='outline'
                    className={`h-24 w-24 p-0 border-3 rounded-lg flex-shrink-0 overflow-hidden ${
                      currentBackgroundImageUrl === image.url
                        ? 'border-[#FFD700] ring-0'
                        : 'border-transparent hover:border-gray-300'
                    }`}
                    onClick={() => onSelectImage(image.url)}
                  >
                    <Image
                      src={image.url}
                      alt={image.name}
                      width={96}
                      height={96}
                      className='w-full h-full object-cover'
                      onError={() => {
                        console.warn(`Failed to load image: ${image.url}`);
                        // 可以设置一个默认图片或隐藏按钮
                      }}
                    />
                  </Button>
                ))}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
